import type { Metadata } from "next";
import InnerPage from "./InnerPage"; 
import { axiosInstance } from "@/lib/axios";
import { parseAndJoinArray } from "@/lib/helper";

interface TeacherDetailsSEOProps {
  params: { id: string };
}

async function fetchTeacherData(id: string) {
  try {
    const res = await axiosInstance.get(`classes/details/${id}`);
    return res.data;
  } catch (err) {
    console.error("Failed to fetch teacher data for SEO", err);
    return null;
  }
}

export async function generateMetadata({ params }: TeacherDetailsSEOProps): Promise<Metadata> {
  const { id } = params;
  const data = await fetchTeacherData(id);

  const { firstName, lastName, ClassAbout, tuitionClasses } = data;
  const fullName = `${firstName} ${lastName}`;
  const profileImg = ClassAbout?.profilePhoto
    ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${ClassAbout.profilePhoto}`
    : "/default-teacher-profile.jpg";
  const catchyHeadline = ClassAbout?.catchyHeadline || "Professional Educator";
  const tutorBio = ClassAbout?.tutorBio || "Explore the expertise of our educators.";
  const tuitionDetails = tuitionClasses?.length > 0
    ? parseAndJoinArray(tuitionClasses[0].subject || tuitionClasses[0].details || [])
    : "Educational Services";

  return {
    title: `${fullName} - ${catchyHeadline} | Uest`,
    description: `${tutorBio} Specializing in ${tuitionDetails}. Book your classes with ${fullName} on Uest today!`,
    keywords: [
      fullName,
      catchyHeadline,
      tuitionDetails,
      "online education",
      "tuition classes",
      "Uest",
      "teacher profile",
    ],
    openGraph: {
      title: `${fullName} - ${catchyHeadline} | Uest`,
      description: `${tutorBio} Specializing in ${tuitionDetails}.`,
      url: `https://www.uest.in/classes-details/${id}`,
      type: "website",
      images: [
        {
          url: profileImg,
          width: 800,
          height: 600,
          alt: `${fullName} Profile`,
        },
      ],
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

export default function TeacherDetailsPage({ params }: TeacherDetailsSEOProps) {
  return <InnerPage />;
}