import { Metadata } from 'next';
import { storeMetadata, storeStructuredData } from './metadata';

export const metadata: Metadata = storeMetadata;

export default function StoreLayout({ children }: { children: React.ReactNode }) {
  return (
    <>
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(storeStructuredData) }}
      />
      {children}
    </>
  );
}
