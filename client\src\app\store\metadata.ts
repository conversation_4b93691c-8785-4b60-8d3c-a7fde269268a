import type { Metadata } from "next";

export const storeMetadata: Metadata = {
  title: "UEST Store - Premium Educational Products | Uest",
  description: "Shop premium educational products at UEST Store. Buy books, stationery, study materials, and educational tools with UEST coins. Exclusive discounts for students and educators.",
  keywords: [
    "UEST store",
    "educational products",
    "student store",
    "educational materials",
    "study materials",
    "books online",
    "stationery",
    "educational tools",
    "student supplies",
    "UEST coins",
    "educational shopping",
    "online store",
    "premium products",
    "student discounts"
  ],
  openGraph: {
    title: "UEST Store - Premium Educational Products | Uest",
    description: "Shop premium educational products at UEST Store. Buy books, stationery, study materials with UEST coins. Exclusive discounts for students.",
    url: "https://www.uest.in/store",
    type: "website",
    images: [
      {
        url: "https://www.uest.in/store-banner.jpg",
        width: 1200,
        height: 630,
        alt: "UEST Store - Premium Educational Products",
      },
    ],
    siteName: "Uest",
  },
  twitter: {
    card: "summary_large_image",
    title: "UEST Store - Premium Educational Products | Uest",
    description: "Shop premium educational products at UEST Store. Buy books, stationery, study materials with UEST coins.",
    images: ["https://www.uest.in/store-banner.jpg"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  alternates: {
    canonical: "https://www.uest.in/store",
  },
  other: {
    "application-name": "Uest",
    "apple-mobile-web-app-title": "Uest - Store",
    "format-detection": "telephone=no",
  },
};

export const storeStructuredData = {
  "@context": "https://schema.org",
  "@type": "Store",
  "name": "UEST Store",
  "description": "Premium educational products for students and educators",
  "url": "https://www.uest.in/store",
  "image": "https://www.uest.in/store-banner.jpg",
  "priceRange": "₹₹",
  "paymentAccepted": ["UEST Coins", "Credit Card", "Debit Card"],
  "currenciesAccepted": "INR",
  "hasOfferCatalog": {
    "@type": "OfferCatalog",
    "name": "Educational Products",
    "itemListElement": [
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Product",
          "name": "Educational Books",
          "category": "Books"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Product",
          "name": "Study Materials",
          "category": "Educational Materials"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Product",
          "name": "Stationery",
          "category": "Office Supplies"
        }
      }
    ]
  },
  "parentOrganization": {
    "@type": "Organization",
    "name": "Uest",
    "url": "https://www.uest.in"
  }
};
