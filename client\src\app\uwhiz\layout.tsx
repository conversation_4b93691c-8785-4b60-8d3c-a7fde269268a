import { Metadata } from 'next';
import { uwhizMetadata, uwhizStructuredData } from './metadata';

export const metadata: Metadata = uwhizMetadata;

export default function UwhizLayout({ children }: { children: React.ReactNode }) {
  return (
    <>
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(uwhizStructuredData) }}
      />
      {children}
    </>
  );
}
