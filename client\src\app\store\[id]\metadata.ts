import type { Metadata } from "next";
import { StoreItem } from "@/lib/types";

// This would typically fetch from your API
async function fetchProductData(id: string): Promise<StoreItem | null> {
  try {
    // Replace with your actual API call
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/admin/store/${id}`, {
      cache: 'no-store'
    });
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error fetching product data:', error);
    return null;
  }
}

export async function generateProductMetadata(id: string): Promise<Metadata> {
  const product = await fetchProductData(id);

  if (!product) {
    return {
      title: "Product Not Found | UEST Store",
      description: "The requested product could not be found in UEST Store.",
    };
  }

  const productImage = product.image 
    ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${product.image}`
    : "/default-product.jpg";

  return {
    title: `${product.name} - UEST Store | Uest`,
    description: `${product.description} Available at UEST Store for ${product.coinPrice} coins. Premium educational product with fast delivery.`,
    keywords: [
      product.name,
      product.category,
      "UEST store",
      "educational products",
      "buy online",
      "student supplies",
      "UEST coins",
      product.category.toLowerCase(),
    ],
    openGraph: {
      title: `${product.name} - UEST Store | Uest`,
      description: `${product.description} Available for ${product.coinPrice} coins.`,
      url: `https://www.uest.in/store/${id}`,
      type: "website",
      images: [
        {
          url: productImage,
          width: 800,
          height: 600,
          alt: product.name,
        },
      ],
      siteName: "Uest",
    },
    twitter: {
      card: "summary_large_image",
      title: `${product.name} - UEST Store`,
      description: `${product.description} Available for ${product.coinPrice} coins.`,
      images: [productImage],
    },
    robots: {
      index: true,
      follow: true,
    },
    alternates: {
      canonical: `https://www.uest.in/store/${id}`,
    },
  };
}

export function generateProductStructuredData(product: StoreItem, id: string) {
  const productImage = product.image 
    ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${product.image}`
    : "/default-product.jpg";

  return {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": product.name,
    "description": product.description,
    "image": productImage,
    "url": `https://www.uest.in/store/${id}`,
    "category": product.category,
    "offers": {
      "@type": "Offer",
      "price": product.coinPrice,
      "priceCurrency": "COINS",
      "availability": product.availableStock > 0 
        ? "https://schema.org/InStock" 
        : "https://schema.org/OutOfStock",
      "seller": {
        "@type": "Organization",
        "name": "UEST Store",
        "url": "https://www.uest.in/store"
      }
    },
    "brand": {
      "@type": "Brand",
      "name": "UEST"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.5",
      "reviewCount": "10"
    }
  };
}
