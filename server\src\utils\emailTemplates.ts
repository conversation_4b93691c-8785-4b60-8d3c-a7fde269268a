import dotenv from 'dotenv';
dotenv.config();


export const createEmailTemplate = (content: string, title?: string): string => {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 8px;">
      <div style="text-align: center; padding-bottom: 20px; border-bottom: 2px solid #ff914d;">
        ${title ? `<h2 style="color: #ff914d; margin-bottom: 5px;">${title}</h2>` : ''}
      </div>

      <div style="padding: 20px 0;">
        ${content}
      </div>

      <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; text-align: center; color: #666; font-size: 12px;">
        <p>This is an automated notification from Uest.in</p>
        <p>© ${new Date().getFullYear()} Uest.in - Your Gateway to Educational Excellence</p>
        <div style="margin-top: 10px;">
          <a href="https://www.uest.in/" style="color: #ff914d; text-decoration: none; margin: 0 5px;">Website</a> |
          <a href="https://www.uest.in/support" style="color: #ff914d; text-decoration: none; margin: 0 5px;">Support</a>
        </div>
        <p style="margin-top: 15px; font-size: 11px;">If you have any questions, please don't hesitate to contact our support team.</p>
      </div>
    </div>
  `;
};

export const createVerificationEmailTemplate = (verificationURL: string, isResend: boolean = false): string => {
  const content = `
    <div style="text-align: center; padding: 20px 0;">
      <h3 style="color: #333; margin-bottom: 15px;">${isResend ? 'Email Verification Reminder' : 'Welcome to Uest.in!'}</h3>
      <p style="margin-bottom: 20px; color: #555; line-height: 1.5;">
        ${isResend
      ? 'You requested to resend your verification email. Please verify your email address to complete your registration and access all features.'
      : 'Thank you for registering with Uest.in. Please verify your email address to complete your registration and access all features.'}
      </p>
      <a href="${verificationURL}"
         style="display: inline-block; padding: 12px 24px; background-color: #ff914d; color: white; text-decoration: none; border-radius: 4px; font-weight: bold;">
        Verify Email Address
      </a>
      <p style="margin-top: 20px; color: #777; font-size: 13px;">
        If you didn't request this verification, please ignore this email.
      </p>
    </div>
  `;

  return createEmailTemplate(content, 'Email Verification');
};

export const createPasswordResetTemplate = (resetURL: string): string => {
  const content = `
    <div style="text-align: center; padding: 20px 0;">
      <h3 style="color: #333; margin-bottom: 15px;">Password Reset Request</h3>
      <p style="margin-bottom: 20px; color: #555; line-height: 1.5;">
        You requested to reset your password. Click the button below to set a new password.
        This link will expire in 1 hour.
      </p>
      <a href="${resetURL}"
         style="display: inline-block; padding: 12px 24px; background-color: #ff914d; color: white; text-decoration: none; border-radius: 4px; font-weight: bold;">
        Reset Password
      </a>
      <p style="margin-top: 20px; color: #777; font-size: 13px;">
        If you didn't request a password reset, please ignore this email or contact support if you have concerns.
      </p>
    </div>
  `;

  return createEmailTemplate(content, 'Password Reset');
};

export const createReviewNotificationTemplate = (
  className: string,
  reviewStudentName: string,
  rating: number,
  message: string
): string => {
  // Generate star rating display
  const starRating = '★'.repeat(rating) + '☆'.repeat(5 - rating);

  const content = `
    <div style="padding: 20px 0;">
      <div style="margin-bottom: 15px;">
        <p style="font-weight: bold; margin-bottom: 5px;">Student Name:</p>
        <p style="margin-top: 0; padding: 10px; background-color: #f9f9f9; border-radius: 4px;">${reviewStudentName}</p>
      </div>

      <div style="margin-bottom: 15px;">
        <p style="font-weight: bold; margin-bottom: 5px;">Rating:</p>
        <p style="margin-top: 0; font-size: 24px; color: #ff914d;">${starRating} (${rating}/5)</p>
      </div>

      <div style="margin-bottom: 15px;">
        <p style="font-weight: bold; margin-bottom: 5px;">Review:</p>
        <p style="margin-top: 0; padding: 10px; background-color: #f9f9f9; border-radius: 4px;">${message}</p>
      </div>
    </div>
  `;

  return createEmailTemplate(content, `New Review Received for ${className}`);
};

export const createNotificationTemplate = (subject: string, message: string): string => {
  const content = `
    <div style="padding: 20px 0;">
      <p style="color: #555; line-height: 1.5;">${message}</p>
    </div>
  `;

  return createEmailTemplate(content, subject);
};

export const createCongratulationsTemplate = (studentName: string): string => {
  const content = `
    <div style="text-align: center; padding: 20px 0;">
      <h3 style="color: #333; margin-bottom: 15px;">Congratulations, ${studentName}!</h3>
      <p style="margin-bottom: 20px; color: #555; line-height: 1.5;">
        Welcome to Uest.in! Your registration has been successfully completed.
      </p>
      <div style="background-color: #f9f9f9; padding: 15px; border-radius: 8px; margin: 20px 0;">
        <p style="color: #555; line-height: 1.5;">
          You now have access to all our educational resources and features. You can:
        </p>
        <ul style="text-align: left; color: #555; line-height: 1.5; margin-top: 10px;">
          <li style="margin-bottom: 8px;">Find and connect with the best tutors in your area</li>
          <li style="margin-bottom: 8px;">Submit reviews for classes and tutors you've experienced</li>
          <li style="margin-bottom: 8px;">Take U-Whiz tests to assess and improve your knowledge</li>
          <li style="margin-bottom: 8px;">Access educational resources to enhance your learning</li>
        </ul>
        <p style="color: #555; line-height: 1.5; margin-top: 10px;">
          We're excited to have you join our community of learners!
        </p>
      </div>
      <p style="margin-top: 20px; color: #555; line-height: 1.5;">
        If you have any questions or need assistance, please don't hesitate to contact our support team.
      </p>
      <a href="${process.env.FRONTEND_URL}"
         style="display: inline-block; margin-top: 15px; padding: 12px 24px; background-color: #ff914d; color: white; text-decoration: none; border-radius: 4px; font-weight: bold;">
        Explore Uest.in
      </a>
    </div>
  `;

  return createEmailTemplate(content, 'Welcome to Uest.in!');
};

export const createAdminNotificationTemplate = (studentDetails: { firstName: string; lastName: string; email: string; contact?: string }): string => {
  const content = `
    <div style="padding: 20px 0;">
      <p style="color: #555; line-height: 1.5; margin-bottom: 15px;">
        A new student has registered on Uest.in. Here are the details:
      </p>

      <div style="background-color: #f9f9f9; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
        <div style="margin-bottom: 10px;">
          <p style="font-weight: bold; margin-bottom: 5px;">Name:</p>
          <p style="margin-top: 0; padding: 8px; background-color: white; border-radius: 4px;">${studentDetails.firstName} ${studentDetails.lastName}</p>
        </div>

        <div style="margin-bottom: 10px;">
          <p style="font-weight: bold; margin-bottom: 5px;">Email:</p>
          <p style="margin-top: 0; padding: 8px; background-color: white; border-radius: 4px;">${studentDetails.email}</p>
        </div>

        ${studentDetails.contact ? `
        <div style="margin-bottom: 10px;">
          <p style="font-weight: bold; margin-bottom: 5px;">Contact:</p>
          <p style="margin-top: 0; padding: 8px; background-color: white; border-radius: 4px;">${studentDetails.contact}</p>
        </div>
        ` : ''}

        <div style="margin-bottom: 10px;">
          <p style="font-weight: bold; margin-bottom: 5px;">Registration Time:</p>
          <p style="margin-top: 0; padding: 8px; background-color: white; border-radius: 4px;">${new Date().toLocaleString()}</p>
        </div>
      </div>

      <p style="color: #555; line-height: 1.5;">
        Please review the student's information in the admin dashboard.
      </p>
    </div>
  `;

  return createEmailTemplate(content, 'New Student Registration');
};

export const createCoinPurchaseTemplate = (
  purchaseDetails: {
    name: string;
    email: string;
    userType: 'STUDENT' | 'CLASS';
    amountPaid: number;
    coinsAdded: number;
    totalCoins: number;
    paymentId: string;
    orderId: string;
  }
): string => {
  const content = `
    <div style="text-align: center; padding: 20px 0;">
      <h3 style="color: #333; margin-bottom: 15px;">Coin Purchase Confirmation</h3>
      <p style="margin-bottom: 20px; color: #555; line-height: 1.5;">
        Thank you for purchasing Uest Coins! Your transaction has been successfully processed.
      </p>

      <div style="background-color: #f9f9f9; padding: 15px; border-radius: 8px; margin: 20px 0;">
        <div style="margin-bottom: 10px;">
          <p style="font-weight: bold; margin-bottom: 5px;">Purchase Details:</p>
          <div style="margin-top: 0; padding: 12px; background-color: white; border-radius: 4px; text-align: left;">
            <p style="margin: 5px 0;"><strong>Amount Paid:</strong> ₹${purchaseDetails.amountPaid.toFixed(2)}</p>
            <p style="margin: 5px 0;"><strong>Coins Added:</strong> ${purchaseDetails.coinsAdded}</p>
            <p style="margin: 5px 0;"><strong>Current Balance:</strong> ${purchaseDetails.totalCoins} Coins</p>
            <p style="margin: 5px 0;"><strong>Transaction ID:</strong> ${purchaseDetails.paymentId}</p>
            <p style="margin: 5px 0;"><strong>Order ID:</strong> ${purchaseDetails.orderId}</p>
            <p style="margin: 5px 0;"><strong>Date & Time:</strong> ${new Date().toLocaleString()}</p>
          </div>
        </div>
      </div>

      <p style="color: #555; line-height: 1.5;">
        You can use these coins for various activities on Uest.in, including applying for exams and accessing premium features.
      </p>

      <a href="${process.env.FRONTEND_URL}/coins"
         style="display: inline-block; margin-top: 15px; padding: 12px 24px; background-color: #ff914d; color: white; text-decoration: none; border-radius: 4px; font-weight: bold;">
        View Coin Balance
      </a>
    </div>
  `;

  return createEmailTemplate(content, 'Uest Coin Purchase Confirmation');
};
