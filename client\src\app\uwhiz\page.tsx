"use client";
import Header from "@/app-components/Header";
import examLogo from "../../../public/exam-logo.jpg";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { getExams } from "@/services/examApi";
import { sendExamApplicantEmail } from "@/services/examApplicantEmailApi";
import { applyForExam } from "@/services/examApplicationApi";
import { useEffect, useState, useMemo } from "react";
import { Exam } from "@/lib/types";
import { FaRegClipboard, FaListOl, FaClock } from "react-icons/fa6";
import { toast } from "sonner";
import { uwhizPreventReattempApi } from "@/services/uwhizPreventReattemptApi";
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronsLeftIcon,
  ChevronsRightIcon,
  Coins,
  Loader2,
} from "lucide-react";
import Footer from "@/app-components/Footer";
import { GiPodiumWinner } from "react-icons/gi";
import { Progress } from "@/components/ui/progress";
import CountdownTimer from "./countDownTimer";
import ExamStatusButton from "./examStatusButton";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { axiosInstance } from "@/lib/axios";
import { getStudentDiscount, calculateDiscountedPrice } from "@/services/referralApi";
import PosterDialog from "@/components/ui/PosterDialog";

const Page = () => {
  const [exams, setExams] = useState<Exam[]>([]);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [selectedExam, setSelectedExam] = useState<Exam | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [limit] = useState(9);
  const [loading, setLoading] = useState(false);
  const [coinError, setCoinError] = useState<string | null>(null);
  const [discountInfo, setDiscountInfo] = useState<{
    hasDiscount: boolean;
    discountPercentage: number;
    referralCode: string | null;
  } | null>(null);
  const [isPaying, setIsPaying] = useState(false);
  const [remCoins, setRemCoins] = useState<number>(0);
  const [isAdding, setIsAdding] = useState(false);
  const [showPosterDialog, setShowPosterDialog] = useState(false);
  const [superKidsError, setSuperKidsError] = useState<string | null>(null);


  const isUpcomingExam = (exam: Exam) => {
    const currentTime = new Date();
    const startTime = new Date(exam.start_date);
    const durationMs = exam.duration * 60 * 1000;
    const endTime = new Date(startTime.getTime() + durationMs);
    return endTime.getTime() > currentTime.getTime();
  };

  const isPastExam = (exam: Exam) => {
    const currentTime = new Date();
    const startTime = new Date(exam.start_date);
    const durationMs = exam.duration * 60 * 1000;
    const endTime = new Date(startTime.getTime() + durationMs);
    return endTime.getTime() < currentTime.getTime();
  };

  const getStudentId = (): string => {
    try {
      const data = localStorage.getItem("student_data");
      return data ? JSON.parse(data).id : "";
    } catch {
      return "";
    }
  };

  const handleOpenPosterDialog = () => {
    setShowPosterDialog(true);
  };

  // Function to handle closing the PosterDialog
  const handleClosePosterDialog = () => {
    setShowPosterDialog(false);
  };

  const memoizedExams = useMemo(() => exams, [exams]);
  const router = useRouter();
  const classId = getStudentId();

  const upcomingExams = memoizedExams.filter(isUpcomingExam);
  const pastExams = memoizedExams.filter(isPastExam);

  useEffect(() => {
    const fetchDiscountInfo = async () => {
      if (classId) {
        try {
          const response = await getStudentDiscount();
          if (response.success) {
            setDiscountInfo(response.data);
          }
        } catch (error) {
          console.error("Error fetching discount info:", error);
        }
      }
    };

    fetchDiscountInfo();
  }, [classId]);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const response = await getExams(currentPage, limit, classId);
        let examsData = response.exams as Exam[];

        // Check attempt status for each exam
        if (classId) {
          examsData = await Promise.all(
            examsData.map(async (exam) => {
              try {
                const attemptResponse = await uwhizPreventReattempApi(classId, exam.id);
                return {
                  ...exam,
                  hasAttempted: attemptResponse.success === false ? false : attemptResponse,
                };
              } catch (error) {
                console.error(`Error checking attempt for exam ${exam.id}:`, error);
                return { ...exam, hasAttempted: false };
              }
            })
          );
        } else {
          examsData = examsData.map((exam) => ({ ...exam, hasAttempted: false }));
        }

        setExams(examsData);
        setTotalPages(response.totalPages || 1);
      } catch (error: any) {
        console.error("Error fetching exams:", error);
        toast.error(error.message || "Failed to load exams");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentPage, limit, classId]);

  const handleApplyClick = (exam: Exam) => {
    setSelectedExam(exam);
    setCoinError(null);
    setShowConfirmDialog(true);
  };


  const fetchCoinData = async () => {
    try {
      const coinsResponse = await axiosInstance.get('/coins/get-total-coins/student');
      return coinsResponse.data.coins;
    } catch (error) {
      toast.error('Failed to load coin data. Please try again.');
      console.error('Error fetching data', error);
    }
  };

  const handleApplyNow = async () => {
    if (!selectedExam) return;
    const classId = getStudentId();

    if (!classId) {
      toast.error("Please log in as a student to apply for an exam");
      return;
    }

    const totalCoins = await fetchCoinData();

    try {
      const applyRes = await applyForExam(selectedExam.id, classId);

      if (applyRes.application) {
        try {
          const studentData = localStorage.getItem('student_data');
          if (studentData) {
            const parsedData = JSON.parse(studentData);
            const studentEmail = parsedData.email;

            if (studentEmail) {
              await sendExamApplicantEmail(
                selectedExam.id,
                selectedExam.exam_name,
                studentEmail
              );
            }
          }
        } catch (emailError) {
          console.log("Email sending failed", emailError);
        }

        setExams((prevExams) =>
          prevExams.map((e) =>
            e.id === selectedExam.id
              ? {
                ...e,
                joinedClassesCount: e.joinedClassesCount + 1,
                totalApplicants: (e.totalApplicants || 0) + 1,
                hasApplied: true,
              }
              : e
          )
        );
        setShowConfirmDialog(false);
        setShowSuccessDialog(true);
        toast.success(applyRes.message || "Successfully applied for the exam");
        setCoinError(null);
      }
    } catch (error: any) {
      const errorMessage = error?.response?.data?.error || error.message || "Error applying for exam";
      toast.error(errorMessage);

      if (errorMessage.includes("Uwhiz Super Kids Exam")) {
        setSuperKidsError(errorMessage);
        setShowConfirmDialog(false);
        return;
      }

      if (errorMessage.includes("Required Coin for Applying in Exam")) {
        setCoinError(errorMessage);

        let coinsRequired = Number(selectedExam.coins_required) ?? 0;
        if (discountInfo?.hasDiscount) {
          coinsRequired = coinsRequired * (1 - discountInfo.discountPercentage / 100);
        }
        const remainingCoins = Math.floor(Math.floor(coinsRequired) - totalCoins);
        setRemCoins(remainingCoins);
      } else {
        setShowConfirmDialog(false);
      }
    } finally {
      setIsPaying(false);
    }
  };

  const closeDialog = () => {
    setShowSuccessDialog(false);
    setShowConfirmDialog(false);
    setSelectedExam(null);
    setCoinError(null);
  };

  const handleViewDetails = (id: string) => {
    window.location.href = `/uwhiz-info/${id}`;
  };

  const calculateProgress = (totalApplicants: number, totalIntake: number) => {
    if (totalIntake === 0) return 0;
    const percentage = (totalApplicants / totalIntake) * 100;
    return Math.min(100, Math.max(0, percentage));
  };

  const initiatePayment = async () => {
    setIsPaying(true);
    try {
      const res = await axiosInstance.post('/coins/create-order', {
        amount: remCoins * 100,
      });

      const { order } = res.data;

      const options = {
        key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,
        amount: order.amount,
        currency: 'INR',
        name: 'Uest Coins',
        description: 'Add Uest Coins',
        order_id: order.id,
        handler: async function (response: any) {
          try {
            setIsAdding(true);

            await axiosInstance.post('/coins/verify', {
              razorpay_order_id: response.razorpay_order_id,
              razorpay_payment_id: response.razorpay_payment_id,
              razorpay_signature: response.razorpay_signature,
              amount: remCoins * 100,
            });

            toast.success('Coins added successfully!');
            handleApplyNow();
            setIsAdding(false);
          } catch {
            toast.error('Payment verification failed');
          } finally {
            setIsAdding(false);
          }
        },
        theme: {
          color: '#f97316',
        },
      };

      const rzp = new (window as any).Razorpay(options);
      rzp.open();
    } catch {
      toast.error('Payment initialization failed');
    } finally {
      setIsPaying(false);
    }
  };

  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://checkout.razorpay.com/v1/checkout.js';
    script.async = true;
    document.body.appendChild(script);
  }, []);

  return (
    <div className="">
      <Header />
      <div className="flex justify-center bg-black pb-10">
        <Image
          height={400}
          width={400}
          src={examLogo.src}
          alt="Uwhiz Competitive Exams Logo - Online Educational Platform"
          priority={true}
          quality={100}
        />
      </div>

      {showSuccessDialog && selectedExam && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
            <h2 className="text-2xl font-bold text-green-600 mb-4">
              Application Successful!
            </h2>
            <p className="text-gray-700 mb-6">
              You have successfully applied for{" "}
              <strong>{selectedExam.exam_name}</strong>.
            </p>
            <Button
              onClick={closeDialog}
              className="w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-2 rounded-lg"
            >
              Close
            </Button>
          </div>
        </div>
      )}

      {showConfirmDialog && selectedExam && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-8 rounded-xl shadow-2xl max-w-md w-full transform transition-all duration-300 scale-100">
            <h2 className="text-3xl font-semibold text-customOrange mb-4 border-b border-gray-200 pb-2">
              Are You Sure?
            </h2>
            <p className="text-gray-700 text-lg mb-6 leading-relaxed">
              Do you want to apply for{" "}
              <strong className="text-customOrange">{selectedExam.exam_name}</strong>?
              {selectedExam.coins_required != null && (
                <span>
                  {" "}
                  This will cost{" "}
                  {discountInfo?.hasDiscount ? (
                    <span>
                      <span className="line-through text-gray-500">{selectedExam.coins_required}</span>{" "}
                      <strong className="text-green-600">
                        {calculateDiscountedPrice(selectedExam.coins_required, discountInfo.discountPercentage)}
                      </strong>{" "}
                      <span className="text-green-600 text-sm">({discountInfo.discountPercentage}% discount applied)</span>
                    </span>
                  ) : (
                    <strong className="text-customOrange">{selectedExam.coins_required}</strong>
                  )}{" "}
                  coins.
                </span>
              )}
            </p>
            {coinError && (
              <div className="flex-col justify-center items-start gap-2 bg-red-50 p-4 rounded-lg mb-6 border border-red-200">
                <div className="flex gap-5 items-center">
                  <svg
                    className="w-5 h-5 text-red-600 mt-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <p className="text-red-600 text-sm font-medium">{coinError}</p>
                </div>
                <Button
                  onClick={() => initiatePayment()}
                  className="mt-5 w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 rounded-lg"
                  disabled={isAdding}
                >
                  {isAdding ? (
                    <span className="flex items-center justify-center gap-2">
                      <Loader2 className="animate-spin w-5 h-5" />
                      Processing...
                    </span>
                  ) : (
                    "Add Coins"
                  )}
                </Button>
              </div>
            )}
            {getStudentId() ? (
              <div className="flex gap-4">
                <Button
                  onClick={handleApplyNow}
                  className="w-1/2 bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 rounded-lg"
                  disabled={!!coinError || isPaying}
                >
                  {isPaying ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    "Yes, Apply"
                  )}
                </Button>
                <Button
                  onClick={closeDialog}
                  className="w-1/2 bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-3 rounded-lg"
                >
                  Cancel
                </Button>
              </div>
            ) : (
              <Button
                onClick={() => router.push('/student/login?redirect=/uwhiz')}
                className="w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 rounded-lg"
              >
                Login to Apply
              </Button>
            )}
          </div>
        </div>
      )}

      {superKidsError && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-xl max-w-md w-full">
            <h2 className="text-2xl font-bold text-red-600 dark:text-red-400 mb-4">
              Access Denied
            </h2>
            <p className="text-gray-700 dark:text-gray-300 mb-6">
              {superKidsError}
            </p>
            <button
              onClick={() => setSuperKidsError(null)}
              className="w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-2 rounded-lg"
            >
              Close
            </button>
          </div>
        </div>
      )}


      {loading ? (
        <p className="text-center text-white mt-6">Loading exams...</p>
      ) : (
        <>
          {/* Upcoming Exams section */}
          {upcomingExams.length > 0 && (
            <section aria-labelledby="upcoming-exams-heading">
              <div className="flex justify-center items-center mt-10">
                <h1 id="upcoming-exams-heading" className="text-3xl font-bold ml-4">
                  Upcoming <span className="text-customOrange">Exams </span>
                </h1>
              </div>
              <div className="flex flex-wrap justify-center items-start gap-6 p-4 sm:p-6 md:p-10 lg:p-20">
                {upcomingExams &&
                  upcomingExams.map((exam) => {
                    const totalIntake = exam.total_student_intake ?? 0;
                    const firstRankPrice =
                      exam.UwhizPriceRank.find((rank: any) => rank.rank === 1)?.price ?? 0;

                    return (
                      <Card
                        key={exam.id}
                        className="w-full sm:w-[45%] lg:w-[30%] dark:bg-[#101828] text-black rounded-xl shadow-lg hover:shadow-2xl hover:scale-105 transform transition-all duration-500 ease-out group border-l-3 border-l-orange-400"
                      >
                        <div className="flex flex-col items-center px-3 py-2 space-y-2 text-center">
                          <h1 className="text-2xl font-bold dark:text-white transition-transform duration-300 group-hover:scale-105">
                            {exam.exam_name}
                          </h1>
                          {exam.id !== 4 && exam.id !== 6 && (
                            <div className="flex items-center gap-2 text-xl font-bold">
                              <GiPodiumWinner className="text-xl text-customOrange" />
                              <span className="dark:text-white">
                                1st Prize: <span className="text-customOrange">{firstRankPrice}</span>
                              </span>
                            </div>
                          ) }
                        </div>
                        <CountdownTimer exam={exam} />
                        <div className="text-sm dark:text-gray-100 mb-3 px-4 space-y-2 dark:group-hover:text-gray-200 transition-colors duration-300">
                          <div className="flex justify-between">
                            <div className="flex gap-2 items-center">
                              <FaListOl className="text-lg text-customOrange" />
                              <span>Total Questions: {exam.total_questions}</span>
                            </div>
                            <div className="flex gap-2 items-center">
                              <FaRegClipboard className="text-lg text-customOrange" />
                              <span>Marks: {exam.marks}</span>
                            </div>
                          </div>
                          <div className="flex justify-between">
                            <div className="flex gap-2 items-center">
                              <FaClock className="text-lg text-customOrange" />
                              <span>Duration: {exam.duration}</span>
                            </div>
                            <div className="flex gap-2 items-center">
                              <Coins className="text-lg text-customOrange" />
                              <span>
                                Coins: { 
                                (
                            
                                  discountInfo?.hasDiscount ? (
                                    <span className="flex items-center gap-1">
                                      <span className="line-through text-gray-500 text-xs">
                                        {exam.coins_required}
                                      </span>
                                      <span className="text-green-600 font-bold">
                                        {calculateDiscountedPrice(exam.coins_required, discountInfo.discountPercentage)}
                                      </span>
                                      <span className="text-xs text-green-600">
                                        ({discountInfo.discountPercentage}% off)
                                      </span>
                                    </span>
                                  ) : (
                                   Number(exam.coins_required) === 0 ? "Free" :exam.coins_required  
                                  )
                                )}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="flex flex-col px-3">
                          <p className="dark:text-white mb-1 tracking-wider">Student Joined</p>

                          <Progress
                            value={calculateProgress(((exam.totalApplicants ?? 0)), totalIntake)}
                            className="[&>*]:bg-customOrange bg-slate-300"
                          />

                          <p className="flex justify-end text-orange-500 text-sm dark:text-white">
                            <span>
                              Limited Seats Available
                            </span>
                          </p>
                        </div>
                        <div className="flex justify-center px-3">
                          {
                            exam.id === 1 && (
                              <Button
                                className="w-1/3 bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"
                                onClick={() => handleViewDetails(String(exam.id))}
                              >
                                View details
                              </Button>
                            )
                          }
                          <ExamStatusButton
                            exam={exam}
                            hasApplied={exam.hasApplied}
                            isMaxLimitReached={exam.isMaxLimitReached}
                            hasAttempted={exam.hasAttempted}
                            onApplyClick={() => handleApplyClick(exam)}
                          />
                        </div>

                        {exam.id === 7 && (
                          <>
                            <div className="bg-customOrange text-white font-semibold text-sm text-center p-3 flex items-center justify-center gap-2  border-orange-600 shadow-md ">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-5 w-5 text-white"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M13 16h-1v-4h-1m1-4h.01M12 20.5a8.5 8.5 0 100-17 8.5 8.5 0 000 17z"
                                />
                              </svg>
                              <span>This exam is only for those who have participated in the Uwhiz Super Kids Exam.</span>
                            </div>
                          </>
                        )}

                        {exam.id === 1 ? (
                          <div className="flex items-center justify-center gap-2 text-sm text-gray-600 dark:text-gray-300 pt-2 border-t border-gray-200 dark:border-gray-700">
                            <span>Sponsored by</span>
                            <Image
                              src="/nalanda.png"
                              alt="Nalanda Logo"
                              height={60}
                              width={60}
                              className="object-contain h-5 w-5"
                            />
                            <span className="font-semibold">Nalanda Vidhyalay</span>
                          </div>
                        ) : (null)}
                      </Card>
                    );
                  })}
              </div>
            </section>
          )}

          {/* Past Exams section */}
          {pastExams.length > 0 &&
            (
              <section aria-labelledby="past-exams-heading">
                <h2 id="past-exams-heading" className="text-center text-4xl font-bold dark:text-white mt-10">
                  Past <span className="text-customOrange">Exams</span>
                </h2>
                {loading ? (
                  <p className="text-center text-white mt-6">Loading exams...</p>
                ) : pastExams.length === 0 ? (
                  <p className="text-center text-white mt-6">No past exams found.</p>
                ) : (
                  <div className="flex flex-wrap justify-center gap-6 p-4 sm:p-6 md:p-10 lg:p-20">
                    {pastExams.map((exam) => {
                      // const totalIntake = exam.total_student_intake ?? 0;
                      const firstRankPrice =
                        exam.UwhizPriceRank.find((rank: any) => rank.rank === 1)?.price ?? 0;

                      return (
                        <Card
                          key={exam.id}
                          className="w-full sm:w-[45%] lg:w-[30%] dark:bg-[#101828] text-black rounded-xl shadow-lg hover:shadow-2xl hover:scale-105 transform transition-all duration-500 ease-out group border-l-3 border-l-orange-400"
                        >
                          <div className="flex flex-col items-center px-3 py-2 space-y-2 text-center">
                            <h1 className="text-2xl font-bold dark:text-white transition-transform duration-300 group-hover:scale-105">
                              {exam.exam_name}
                            </h1>
                            {(exam.id !== 4 && exam.id !== 6) && (
                              <div className="flex items-center gap-2 text-xl font-bold">
                                <GiPodiumWinner className="text-xl text-customOrange" />
                                <span className="dark:text-white">
                                  1st Prize: <span className="text-customOrange">{firstRankPrice}</span>
                                </span>
                              </div>
                            )}
                          </div>
                          <CountdownTimer exam={exam} />
                          <div className="text-sm dark:text-gray-100 mb-3 px-4 space-y-2 dark:group-hover:text-gray-200 transition-colors duration-300">
                            <div className="flex justify-between">
                              <div className="flex gap-2 items-center">
                                <FaListOl className="text-lg text-customOrange" />
                                <span>Total Questions: {exam.total_questions}</span>
                              </div>
                              <div className="flex gap-2 items-center">
                                <FaRegClipboard className="text-lg text-customOrange" />
                                <span>Marks: {exam.marks}</span>
                              </div>
                            </div>
                            <div className="flex justify-between">
                              <div className="flex gap-2 items-center">
                                <FaClock className="text-lg text-customOrange" />
                                <span>Duration: {exam.duration}</span>
                              </div>
                              <div className="flex gap-2 items-center">
                                <Coins className="text-lg text-customOrange" />
                                <span>
                                  Coins: {exam.coins_required != null ? (
                                    discountInfo?.hasDiscount ? (
                                      <span className="flex items-center gap-1">
                                        <span className="line-through text-gray-500 text-xs">
                                          {exam.coins_required}
                                        </span>
                                        <span className="text-green-600 font-bold">
                                          {calculateDiscountedPrice(exam.coins_required, discountInfo.discountPercentage)}
                                        </span>
                                        <span className="text-xs text-green-600">
                                          ({discountInfo.discountPercentage}% off)
                                        </span>
                                      </span>
                                    ) : (
                                      exam.coins_required
                                    )
                                  ) : "Free"}
                                </span>
                              </div>
                            </div>
                          </div>
                          {
                            exam.id === 5 ? (
                              <div className="flex flex-col px-3">
                                <p className="dark:text-white mb-1 tracking-wider">Classes Joined</p>
                                <Progress
                                  value={100}
                                  className="[&>*]:bg-customOrange bg-slate-300"
                                />
                                <p className="flex justify-end text-orange-500 text-sm dark:text-white">
                                  <span>
                                    Seats Full
                                  </span>
                                </p>
                              </div>
                            ) : (
                              <div className="flex flex-col px-3">
                                <p className="dark:text-white mb-1 tracking-wider">Student Joined</p>
                                <Progress
                                  value={100}
                                  className="[&>*]:bg-customOrange bg-slate-300"
                                />
                                <p className="flex justify-end text-orange-500 text-sm dark:text-white">
                                  <span>
                                    {/* {exam.totalApplicants} / {totalIntake} */}
                                    Seats Full
                                  </span>
                                </p>
                              </div>
                            )
                          }
                          <div className="flex justify-center px-3">
                            {exam.id === 1 && (

                              <div className="flex gap-2 w-full justify-center">
                                <Button
                                  className="w-1/3 bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"
                                  onClick={() => handleViewDetails(String(exam.id))}
                                >
                                  View details
                                </Button>
                                <Button
                                  className="w-1/3 bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"
                                  onClick={() => router.push(`uwhiz-super-kids-result`)}
                                >
                                  View Result
                                </Button>
                              </div>
                            )}
                            {exam.id === 3 && (
                              <>
                                <Button
                                  className="bg-customOrange mx-5"
                                  onClick={handleOpenPosterDialog}
                                >
                                  View Result
                                </Button>
                                <PosterDialog open={showPosterDialog} onClose={handleClosePosterDialog} />
                              </>
                            )}
                            {exam.id === 5 ? (
                              <Button
                                className="bg-customOrange mx-5"
                                onClick={() => router.push(`/uwhiz-details/${5}`)}
                              >
                                View Result
                              </Button>
                            ) : exam.id !== 3 && exam.id !== 1 ? (
                              <ExamStatusButton
                                exam={exam}
                                hasApplied={exam.hasApplied}
                                isMaxLimitReached={exam.isMaxLimitReached}
                                hasAttempted={exam.hasAttempted}
                                onApplyClick={() => handleApplyClick(exam)}
                              />
                            ) : null}
                          </div>
                        </Card>
                      );
                    })}
                  </div>
                )}
              </section>
            )}

          {/* Pagination section */}
          <div className="flex items-center justify-center px-4 py-6 dark:text-white">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => setCurrentPage(1)}
                disabled={currentPage === 1}
                className="bg-white dark:text-white hover:bg-gray-200 cursor-pointer"
              >
                <ChevronsLeftIcon />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="bg-white dark:text-white hover:bg-gray-200 cursor-pointer"
              >
                <ChevronLeftIcon />
              </Button>
              <span className="text-sm">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outline"
                size="icon"
                onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="hover:bg-gray-200 cursor-pointer"
              >
                <ChevronRightIcon />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => setCurrentPage(totalPages)}
                disabled={currentPage === totalPages}
                className="bg-white dark:text-white hover:bg-gray-200 cursor-pointer"
              >
                <ChevronsRightIcon />
              </Button>
            </div>
          </div>
        </>
      )}
      <Footer />
    </div>
  );
};

export default Page;